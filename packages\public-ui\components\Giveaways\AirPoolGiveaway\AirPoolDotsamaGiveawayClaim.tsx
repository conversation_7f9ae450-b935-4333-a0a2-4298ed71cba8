import { SuccessAlertBox } from '@Components/AlertBox';
import toaster from '@Components/Toaster/Toaster';
import DotsamaWallet from '@Components/Web3Wallet/Dotsama/DotsamaWallet';
import { convertToSs58Address } from '@Root/helpers/dotsama';
import {
  Blockchain,
  BlockchainAsset,
  BlockchainPool,
  Giveaway,
  ProjectEvent,
  RewardStatus,
  Web3WalletType,
} from '@airlyft/types';
import { formatAmount } from '@airlyft/web3-evm';
import { DotsamaConnectorData } from '@airlyft/web3-evm-hooks';
import { BigNumber } from 'ethers';
import { useState } from 'react';
import GiveawayTransactionHash from '../GiveawayTransactionHash';
import { useGetUserEventRewards } from '../hooks/useGetUserEventRewards';
import { useDotsamaAirPoolGiveawayClaim } from './useAirPoolGiveawayClaim';
import { useTranslation } from 'next-i18next';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { RecaptchaDeclaration } from '@Components/RecaptchaDeclaration';
import { useGtmTrack } from '@Root/services/tracking';

const AirPoolDotsamaGiveawayClaim = ({
  giveaway,
  projectEvent,
  blockchain,
  asset,
  amount,
  airPool,
}: {
  blockchain: Blockchain;
  giveaway: Giveaway;
  projectEvent: ProjectEvent;
  asset: BlockchainAsset;
  amount: BigNumber;
  airPool: BlockchainPool;
}) => {
  const { claimRewardTrack } = useGtmTrack();
  const { claim } = useDotsamaAirPoolGiveawayClaim(
    projectEvent.project.id,
    projectEvent.id,
    giveaway.id,
    asset,
    airPool,
  );
  const [isClaiming, setIsClaiming] = useState(false);
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { data: userEventRewardsData, loading: isUserEventRewardsLoading } =
    useGetUserEventRewards(projectEvent.id);

  const processing = userEventRewardsData?.userEventRewards.find(
    (item) =>
      item.status === RewardStatus.PROCESSING &&
      item.giveawayId === giveaway.id,
  );

  const txHash = [...(userEventRewardsData?.userEventRewards || [])]
    .sort((a, b) => {
      const dateA = +new Date(a.updatedAt);
      const dateB = +new Date(b.updatedAt);
      return dateB - dateA;
    })
    .find((reward) => reward.txHash)?.txHash;

  const handleSubmit = async (connectorData: DotsamaConnectorData) => {
    setIsClaiming(true);
    let captcha: string | undefined;

    if (projectEvent.ipProtect) {
      if (!executeRecaptcha) {
        toaster({
          title: 'Failed',
          text: 'Recaptcha not initialized',
          type: 'error',
        });
        setIsClaiming(false);
        return;
      }
      captcha = await executeRecaptcha('airpool_dotsama_giveaway_claim');
    }

    const { account } = connectorData;
    if (account) {
      const formattedAddress = convertToSs58Address(
        account,
        blockchain.chainId,
      );
      const formattedConnectorData = {
        ...connectorData,
        account: formattedAddress,
      };

      claim({
        connectorData: formattedConnectorData,
        onError: (err) => {
          toaster({
            title: 'Failed',
            text: err.message,
            type: 'error',
          });
          setIsClaiming(false);
        },
        onSuccess: () => {
          claimRewardTrack({
            projectId: projectEvent.project.id,
            eventId: projectEvent.id,
            projectTitle: projectEvent.project.name,
            eventTitle: projectEvent.title,
            giveawayId: giveaway.id,
            giveawayTitle: giveaway.title || '',
          });
          toaster({
            title: 'Submitted',
            text: 'Your claim request has been submitted, check your notifications for an update.',
            type: 'success',
          });
          setIsClaiming(false);
        },
        captcha,
      });
    } else {
      setIsClaiming(false);
    }
  };

  const { t } = useTranslation('translation');

  if (processing) {
    return (
      <SuccessAlertBox
        title={t('giveaway.airTokenPool.successTitle')}
        subtitle={t('giveaway.airTokenPool.successSubtitle')}
      />
    );
  }

  if (!amount.isZero()) {
    return (
      <div className="space-y-4">
        <DotsamaWallet
          blockchain={blockchain}
          button={{
            confirm: {
              enable: true,
              loading: isClaiming || isUserEventRewardsLoading,
              text: `Claim ${formatAmount(
                amount?.toString(),
                asset.decimals,
              )} ${asset.ticker} using `,
            },
          }}
          onSuccess={handleSubmit}
          excludedWallets={[Web3WalletType.DOTSAMA_MANUAL]}
        />
        {projectEvent.ipProtect && (
          <RecaptchaDeclaration className="text-xs text-cs text-center" />
        )}
      </div>
    );
  }

  return <GiveawayTransactionHash txHash={txHash} blockchain={blockchain} />;
};

export default AirPoolDotsamaGiveawayClaim;
